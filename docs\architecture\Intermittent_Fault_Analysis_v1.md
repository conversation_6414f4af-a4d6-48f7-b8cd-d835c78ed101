# 小车间歇性停车故障分析报告

## 🚨 核心问题发现

### 根本原因：Switch语句逻辑错误
**位置**：`empty.c` 第502-523行 `update_target_pulses()` 函数

**问题描述**：
- `circle_num` 取值范围：0-4（通过 `circle_num = (++circle_num) % 5` 确定）
- `switch` 语句case范围：1-5
- **致命缺陷**：当 `circle_num = 0` 时，没有匹配的case，`target_pulses` 保持未定义值！

### 故障机制分析

#### 正常情况
- circle_num = 1,2,3,4 → 正确设置target_pulses
- 小车按预期圈数停车

#### 故障情况  
- circle_num = 0 → switch无匹配case → target_pulses保持随机值
- 如果target_pulses很大 → 小车一直跑不停
- 如果target_pulses很小 → 小车立即停止

## 🔍 间歇性故障的原因

### 为什么是间歇性的？
1. **内存状态依赖**：target_pulses的初始值取决于内存状态
2. **运行历史影响**：之前运行可能设置了不同的target_pulses值
3. **所有圈数都受影响**：因为circle_num=0在每个按键循环中都会出现

### 故障触发条件
- 按键1按下次数为5的倍数时（circle_num回到0）
- 系统重启后第一次设置圈数
- 内存中target_pulses为异常值时

## 🛠️ 完整解决方案

### 方案A：修复Switch语句（推荐）
```c
void update_target_pulses(void)
{
    switch(circle_num) 
    {
        case 0:
            target_pulses = 17600;        // 1圈
            break;
        case 1:
            target_pulses = 17600 * 2;    // 2圈
            break;
        case 2:
            target_pulses = 17600 * 3;    // 3圈
            break;
        case 3:
            target_pulses = 17600 * 4;    // 4圈
            break;
        case 4:
            target_pulses = 17600 * 5;    // 5圈
            break;
        default:
            target_pulses = 17600;        // 默认1圈
            break;
    }
}
```

### 方案B：调整circle_num逻辑
```c
// 在按键处理中修改
circle_num = (circle_num % 5) + 1;  // 范围改为1-5
```

## 🔧 其他稳定性改进

### 1. 数据类型优化
```c
// 将编码器变量统一为int类型，避免溢出
volatile int g_EncoderACount = 0;
volatile int g_EncoderBCount = 0;
```

### 2. 中断安全保护
```c
// 读取编码器时禁用中断
__disable_irq();
int encoder_a_snapshot = g_EncoderACount;
int encoder_b_snapshot = g_EncoderBCount;
__enable_irq();
```

### 3. 按键处理优化
```c
// 移除阻塞性延时，使用状态机实现LED闪烁
// 避免影响主控制循环的实时性
```

## 📊 验证方法

### 调试代码添加
```c
// 在update_target_pulses()后添加
printf("Circle: %d, Target: %d\n", circle_num, target_pulses);
```

### 测试步骤
1. 连续按键1测试5次，观察target_pulses变化
2. 每个圈数设定重复测试10次
3. 验证编码器计数的准确性
4. 确认停车距离的一致性

## 🎯 预期效果

修复后将实现：
- ✅ 100%可靠的圈数设定
- ✅ 消除间歇性故障
- ✅ 稳定的停车控制
- ✅ 可预测的系统行为
