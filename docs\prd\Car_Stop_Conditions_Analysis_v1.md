# 小车停止条件完整分析报告

## 核心停止条件总览

### 1. 距离控制停止（主要停止机制）
**触发条件**：`check_distance()` 函数返回 `true`
**实现位置**：`empty.c` 第143-148行
**停止逻辑**：
```c
if(check_distance()) {
    // 停车：直接设置PWM为0
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C0_IDX);
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
    break;  // 彻底退出主循环
}
```

**距离计算机制**：
- 基于编码器脉冲计数：`total_pulses = net_encoder_a + net_encoder_b`
- 目标脉冲数：`target_pulses`（默认17600，约400cm）
- 停止判断：`total_pulses >= target_pulses`

### 2. 启动控制条件
**前置条件**：`car_started` 必须为 `true`
**启动方式**：按键2（BTN_START）按下触发
**代码位置**：`empty.c` 第492-498行
```c
if(btn_start_current && !btn_start_pressed && !car_started) {
    car_started = true;               // 启动小车
    start_encoder_a = g_EncoderACount; // 记录起始编码器值
    start_encoder_b = g_EncoderBCount;
    is_slowing = false;               // 重置减速状态
}
```

### 3. Flag_Stop 全局停止标志
**当前状态**：`Flag_Stop = 0`（启动状态）
**影响范围**：
- 循迹传感器读取：`xunji()` 函数中第398行检查
- 速度控制：`balance.c` 第159行清除积分
- 显示状态：`show.c` 第74-77行显示ON/OFF

### 4. 蓝牙控制停止
**触发方式**：接收蓝牙命令 `'a'`
**代码位置**：`bluetooth.c` 第96行
```c
if( uart_receive == 'a' ) Flag_Stop=!Flag_Stop;
```

## 停止条件优先级

### 高优先级（立即停止）
1. **距离达标停止**：`check_distance()` 返回true
2. **主循环break**：直接退出控制循环

### 中优先级（功能性停止）
3. **Flag_Stop标志**：影响传感器读取和控制逻辑
4. **未启动状态**：`car_started = false`

### 低优先级（外部控制）
5. **蓝牙命令停止**：通过Flag_Stop间接控制

## 当前配置分析

### 圈数设置
- 默认圈数：`circle_num = 0`（对应1圈）
- 可选范围：1-5圈
- 脉冲计算：每圈17600脉冲

### 减速机制
- 减速区间：目标前2000脉冲开始减速
- 减速速度：从1800降至1200
- 减速标志：`is_slowing`

## 停止后状态

### 电机控制
- PWM输出：强制设为0
- 左右电机：同时停止
- 控制循环：彻底退出（break）

### 状态指示
- LED指示：快速闪烁表示任务完成
- OLED显示：显示OFF状态
- 编码器：保持最终计数值

## 关键变量状态表

| 变量名 | 当前值 | 作用 | 停止影响 |
|--------|--------|------|----------|
| `car_started` | false→true | 启动控制 | 必须为true才能运行 |
| `Flag_Stop` | 0 | 全局停止标志 | 1时停止传感器读取 |
| `target_pulses` | 17600 | 目标距离 | 达到时触发停止 |
| `total_pulses` | 累计值 | 当前距离 | 与target_pulses比较 |
| `is_slowing` | false | 减速状态 | 影响速度但不停止 |
